import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-feature-header',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
  ],
  template: `
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i [class]="iconClass"></i>
          {{ title }}
        </h1>
        <p class="page-description" *ngIf="description">
          {{ description }}
        </p>
      </div>
      
      <!-- Configuration Section -->
      <div class="config-section" *ngIf="showConfigButton">
        <p-button
          [label]="configButtonLabel"
          [icon]="configButtonIcon"
          severity="secondary"
          [raised]="true"
          (onClick)="onConfigClick()">
        </p-button>
      </div>
    </div>
  `,
  styles: [`
    .page-header {
      margin-bottom: 2rem;
      position: relative;
    }

    .header-content {
      text-align: center;
      margin-bottom: 1.5rem;
    }

    .page-title {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 1rem;
    }

    .page-title i {
      font-size: 2.25rem;
      background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .page-description {
      font-size: 1.1rem;
      color: #6b7280;
      margin: 0;
      line-height: 1.6;
    }

    .config-section {
      position: absolute;
      top: 0;
      right: 0;
    }

    .config-section :host ::ng-deep .p-button {
      background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
      border: none;
      color: white;
      font-weight: 600;
      padding: 0.75rem 2rem;
      border-radius: 25px;
      box-shadow: 0 4px 15px rgba(85, 33, 190, 0.3);
      transition: all 0.3s ease;
    }

    .config-section :host ::ng-deep .p-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(85, 33, 190, 0.4);
    }

    .config-section :host ::ng-deep .p-button:active {
      transform: translateY(0);
    }

    .config-section :host ::ng-deep .p-button .p-button-icon {
      margin-right: 0.5rem;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .page-title {
        font-size: 2rem;
      }
      
      .page-title i {
        font-size: 1.75rem;
      }
      
      .config-section {
        position: static;
        display: flex;
        justify-content: center;
        margin-top: 1rem;
      }
      
      .config-section :host ::ng-deep .p-button {
        padding: 0.5rem 1.5rem;
        font-size: 0.875rem;
      }
    }
  `]
})
export class FeatureHeaderComponent {
  @Input() title: string = '';
  @Input() description?: string;
  @Input() iconClass: string = 'pi pi-star';
  @Input() showConfigButton: boolean = false;
  @Input() configButtonLabel: string = 'Configuration';
  @Input() configButtonIcon: string = 'pi pi-cog';
  
  @Output() configClick = new EventEmitter<void>();

  onConfigClick(): void {
    this.configClick.emit();
  }
}
