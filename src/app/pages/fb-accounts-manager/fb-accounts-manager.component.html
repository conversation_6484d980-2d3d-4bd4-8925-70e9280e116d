<div class="page-container">
  <div class="page-header">
    <div class="header-content">
      <div class="header-info">
        <h1 class="page-title">
          <i class="fab fa-facebook-f"></i>
          Facebook Accounts Manager
        </h1>
        <p class="page-description">
          Manage your Facebook applications and authentication tokens
        </p>
      </div>
    </div>
  </div>

  <div class="content-container">
    <!-- Facebook Apps Cards -->
    <div class="apps-section">
      <div class="section-header">
        <div class="section-title">
          <h2>Facebook Applications</h2>
          <p>Manage your Facebook apps and connected accounts</p>
        </div>
        <div class="section-actions">
          <p-button
            (onClick)="openInstructionsDialog()"
            [outlined]="true"
            icon="pi pi-info-circle"
            label="App Creation Instructions">
          </p-button>
          <p-button
            (onClick)="openAppDialog()"
            icon="pi pi-plus"
            label="Add Facebook App"
            severity="primary">
          </p-button>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="loading-grid">
        <div *ngFor="let item of [1,2,3]" class="app-card-skeleton">
          <p-skeleton borderRadius="12px" height="200px"></p-skeleton>
        </div>
      </div>

      <!-- Apps Grid -->
      <div *ngIf="!loading && facebookApps.length > 0" class="apps-grid">
        <div *ngFor="let app of facebookApps" class="app-card">
          <!-- App Header -->
          <div class="app-header">
            <div class="app-info">
              <div class="app-icon-wrapper">
                <i class="fab fa-facebook-f"></i>
              </div>
              <div class="app-details">
                <h3 class="app-name">{{ app.name }}</h3>
                <code class="app-id">{{ app.app_id }}</code>
              </div>
            </div>
            <div class="app-status">
              <p-tag
                [severity]="app.is_active ? 'success' : 'danger'"
                [value]="app.is_active ? 'Active' : 'Inactive'"
                size="small">
              </p-tag>
            </div>
          </div>

          <!-- App Meta -->
          <div class="app-meta">
            <div class="meta-item">
              <i class="pi pi-calendar"></i>
              <span>Created {{ formatDate(app.created_at!) }}</span>
            </div>
            <div class="meta-item">
              <i class="pi pi-users"></i>
              <span>{{ getTokenCount(app.id!) }} users</span>
            </div>
            <div *ngIf="hasExpiredTokens(app.id!)" class="meta-item expired-warning">
              <i class="pi pi-exclamation-triangle"></i>
              <span>{{ getExpiredTokenCount(app.id!) }} expired</span>
            </div>
          </div>

          <!-- Users -->
          <div class="connected-accounts">
            <div class="accounts-header">
              <h4>
                <i class="pi pi-users"></i>
                Users
              </h4>
              <p-button
                (onClick)="loginWithFacebookForApp(app)"
                [loading]="isLoggingInForApp === app.id"
                [outlined]="true"
                icon="fab fa-facebook-f"
                label="Connect"
                severity="primary">
              </p-button>
            </div>

            <!-- User Accounts List -->
            <div class="accounts-list">
              <div *ngIf="getAppTokens(app.id!).length === 0" class="no-accounts">
                <i class="pi pi-user-plus"></i>
                <span>No users connected</span>
              </div>

              <div *ngFor="let user of getAppTokens(app.id!)" class="account-item">
                <div class="account-info">
                  <div class="account-avatar">
                    <img
                      *ngIf="user.profile_picture_url; else defaultAvatar"
                      [alt]="user.name"
                      [src]="user.profile_picture_url"
                      class="profile-picture">
                    <ng-template #defaultAvatar>
                      <i class="pi pi-user"></i>
                    </ng-template>
                  </div>
                  <div class="account-details">
                    <span class="account-name">
                      {{ user.name }}
                    </span>
                    <div class="account-meta">
                      <p-tag
                        severity="success"
                        size="small"
                        value="Connected">
                      </p-tag>
                      <p-tag
                        *ngIf="user.ad_accounts_count !== undefined"
                        severity="info"
                        size="small"
                        [value]="user.ad_accounts_count + ' Ad Accounts'">
                      </p-tag>
                      <p-tag
                        *ngIf="user.expires_at"
                        [severity]="getExpirationSeverity(user.expires_at)"
                        [value]="'Expires: ' + getTimeUntilExpiration(user.expires_at)"
                        size="small">
                      </p-tag>
                      <span class="token-date">Last login: {{ formatDate(user.last_login || user.created_at!) }}</span>
                    </div>
                  </div>
                </div>
                <div class="account-actions">
                  <p-button
                    (onClick)="editUserAccounts(user)"
                    icon="pi pi-cog"
                    pTooltip="Edit Ad Accounts"
                    severity="secondary"
                    styleClass="action-button">
                  </p-button>
                  <p-button
                    (onClick)="deleteToken(user)"
                    icon="pi pi-trash"
                    pTooltip="Remove User"
                    severity="danger"
                    styleClass="action-button">
                  </p-button>
                </div>
              </div>
            </div>
          </div>

          <!-- App Actions -->
          <div class="app-actions">
            <p-button
              (onClick)="openAppDialog(app)"
              [outlined]="true"
              icon="pi pi-pencil"
              label="Edit"
              severity="secondary">
            </p-button>
            <p-button
              (onClick)="deleteApp(app)"
              [outlined]="true"
              icon="pi pi-trash"
              label="Delete"
              severity="danger">
            </p-button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="!loading && facebookApps.length === 0" class="empty-state">
        <div class="empty-content">
          <div class="empty-icon">
            <i class="fab fa-facebook-f"></i>
          </div>
          <h3>No Facebook Apps</h3>
          <p>Add your first Facebook application to get started with social media management</p>
          <p-button
            (onClick)="openAppDialog()"
            icon="pi pi-plus"
            label="Add Facebook App"
            severity="primary">
          </p-button>
        </div>
      </div>
    </div>
  </div>

  <!-- Add/Edit App Dialog -->
  <p-dialog
    [(visible)]="showAppDialog"
    [closable]="true"
    [draggable]="false"
    [header]="editingApp ? 'Edit Facebook App' : 'Add Facebook App'"
    [modal]="true"
    [resizable]="false"
    styleClass="app-dialog">

    <div class="dialog-content">
      <!-- Dialog Header Info -->
      <div class="dialog-info">
        <div class="dialog-icon">
          <i class="fab fa-facebook-f"></i>
        </div>
        <div class="dialog-description">
          <p>Configure your Facebook application credentials to enable Facebook integration and token management.</p>
          <div class="help-links">
            <a class="help-link" href="https://developers.facebook.com/apps/" target="_blank">
              <i class="pi pi-external-link"></i>
              Facebook Developer Console
            </a>
          </div>
        </div>
      </div>

      <!-- Form Fields -->
      <div class="form-container">
        <div class="form-group">
          <label class="form-label" for="appName">
            <i class="pi pi-tag"></i>
            App Name *
          </label>
          <input
            [(ngModel)]="appForm.name"
            class="form-input"
            id="appName"
            pInputText
            placeholder="e.g., My Company Facebook App"
            type="text"/>
          <small class="form-help">A friendly name to identify this Facebook app</small>
        </div>

        <div class="form-group">
          <label class="form-label" for="appId">
            <i class="pi pi-id-card"></i>
            App ID *
          </label>
          <input
            [(ngModel)]="appForm.app_id"
            class="form-input"
            id="appId"
            pInputText
            placeholder="1234567890123456"
            type="text"/>
          <small class="form-help">Your Facebook App ID from the Developer Console</small>
        </div>

        <div class="form-group">
          <label class="form-label" for="appSecret">
            App Secret *
          </label>
          <input
            [(ngModel)]="appForm.app_secret"
            class="form-input"
            id="appSecret"
            pInputText
            placeholder="Enter your Facebook App Secret"
            type="password"/>
          <small class="form-help">Your Facebook App Secret (keep this secure!)</small>
        </div>

        <div class="form-group checkbox-group">
          <div class="checkbox-wrapper">
            <input
              [(ngModel)]="appForm.is_active"
              class="form-checkbox"
              id="isActive"
              type="checkbox"/>
            <label class="checkbox-label" for="isActive">
              <span class="checkbox-text">Active</span>
              <small class="checkbox-help">Enable this app for token generation</small>
            </label>
          </div>
        </div>
      </div>
    </div>

    <ng-template pTemplate="footer">
      <div class="dialog-footer">
        <p-button
          (onClick)="closeAppDialog()"
          [outlined]="true"
          icon="pi pi-times"
          label="Cancel"
          severity="secondary">
        </p-button>
        <p-button
          (onClick)="saveApp()"
          [icon]="editingApp ? 'pi pi-check' : 'pi pi-plus'"
          [label]="editingApp ? 'Update App' : 'Create App'"
          severity="primary">
        </p-button>
      </div>
    </ng-template>
  </p-dialog>


  <!-- App Creation Instructions Dialog -->
  <p-dialog
    [(visible)]="showInstructionsDialog"
    [closable]="true"
    [draggable]="false"
    [modal]="true"
    [resizable]="false"
    header="Facebook App Creation Instructions"
    styleClass="instructions-dialog">

    <div class="instructions-content">
      <!-- Header Info -->
      <div class="instructions-header">
        <div class="header-icon">
          <i class="fab fa-facebook-f"></i>
        </div>
        <div class="header-text">
          <h3>Create Your Facebook App</h3>
          <p>Follow these step-by-step instructions to create a Facebook app for business use</p>
        </div>
      </div>

      <!-- Instructions Steps -->
      <div class="instructions-steps">
        <div class="step-item">
          <div class="step-number">1</div>
          <div class="step-content">
            <h4>Access Facebook Developers</h4>
            <p>Login to <a class="external-link" href="https://developers.facebook.com/" target="_blank">
              <i class="pi pi-external-link"></i>
              https://developers.facebook.com/
            </a></p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">2</div>
          <div class="step-content">
            <h4>Navigate to My Apps</h4>
            <p>Click on <strong>"My Apps"</strong> in the top navigation</p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">3</div>
          <div class="step-content">
            <h4>Create New App</h4>
            <p>Click <strong>"Create App"</strong> button</p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">4</div>
          <div class="step-content">
            <h4>App Details</h4>
            <p>Enter your <strong>App name</strong> and <strong>App contact email</strong></p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">5</div>
          <div class="step-content">
            <h4>App Experience</h4>
            <p>Select <strong>"Other"</strong> - Your app will be created in the old experience. Then, you'll choose
              from all available permissions, features and products.</p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">6</div>
          <div class="step-content">
            <h4>App Type</h4>
            <p>Select an app type → <strong>"Business"</strong></p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">7</div>
          <div class="step-content">
            <h4>Create App</h4>
            <p>Click <strong>"Create app"</strong> to proceed</p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">8</div>
          <div class="step-content">
            <h4>Add Facebook Login</h4>
            <p>Add products to your app → <strong>"Facebook Login For Business"</strong> → <strong>"Set up"</strong></p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">9</div>
          <div class="step-content">
            <h4>Enable JavaScript SDK</h4>
            <p>Check <strong>"Login with JavaScript SDK"</strong></p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">10</div>
          <div class="step-content">
            <h4>Configure Domain</h4>
            <p>In the <strong>"Allowed Domains for the JavaScript SDK"</strong> section, enter the domain:</p>
            <div class="domain-section">
              <code class="domain-code">{{ currentHost }}</code>
              <p-button
                (onClick)="copyToClipboard(currentHost)"
                [text]="true"
                icon="pi pi-copy"
                pTooltip="Copy domain"
                size="small">
              </p-button>
            </div>
            <p class="domain-note">
              <i class="pi pi-info-circle"></i>
              This ensures that login and signed-in functionality of the JavaScript SDK will work on your domain.
            </p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">11</div>
          <div class="step-content">
            <h4>Save Changes</h4>
            <p>Click <strong>"Save changes"</strong></p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">12</div>
          <div class="step-content">
            <h4>Basic Settings</h4>
            <p>Go to <strong>"App settings"</strong> → <strong>"Basic"</strong></p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">13</div>
          <div class="step-content">
            <h4>Privacy & Terms</h4>
            <p>Add <strong>"Privacy Policy URL"</strong> and <strong>"Terms of Service URL"</strong></p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">14</div>
          <div class="step-content">
            <h4>Save Settings</h4>
            <p>Click <strong>"Save changes"</strong></p>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">15</div>
          <div class="step-content">
            <h4>Go Live</h4>
            <p>Set <strong>"App Mode to Live"</strong> (toggle at the top of the page)</p>
          </div>
        </div>

        <div class="step-item final-step">
          <div class="step-number">16</div>
          <div class="step-content">
            <h4>Add to Chainmatic</h4>
            <p>Now you can add your Facebook App using the <strong>App name</strong>, <strong>App ID</strong> and
              <strong>App secret</strong></p>
            <div class="final-action">
              <p-button
                (onClick)="closeInstructionsAndOpenApp()"
                icon="pi pi-plus"
                label="Add Facebook App Now"
                severity="primary">
              </p-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </p-dialog>

  <!-- Account Selection Dialog -->
  <app-account-selection-dialog
    [visible]="showAccountSelectionDialog"
    [selectionData]="accountSelectionData"
    (result)="onAccountSelectionResult($event)">
  </app-account-selection-dialog>

  <!-- Toast Messages -->
  <p-toast></p-toast>

  <!-- Confirmation Dialog -->
  <p-confirmDialog></p-confirmDialog>
</div>
