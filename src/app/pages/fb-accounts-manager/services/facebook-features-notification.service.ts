import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmationService } from 'primeng/api';
import { ConfigService } from '../../../core';
import { PortalFeature } from '../../../core/types';

@Injectable()
export class FacebookFeaturesNotificationService {
  constructor(
    private confirmationService: ConfirmationService,
    private configService: ConfigService,
    private router: Router,
  ) {}

  /**
   * Show confirmation dialog about affected Facebook features after account changes
   */
  showAffectedFeaturesInfo(): void {
    const fbConfig = this.configService.getCustomerFeatureConfig(
      PortalFeature.FB_ACCOUNTS_MANAGER,
    ) as any;

    if (!fbConfig?.facebookFeatures || fbConfig.facebookFeatures.length === 0) {
      return;
    }

    // Get enabled features from navigation
    const enabledFeatures = this.configService.navigation;

    // Filter to only show enabled Facebook features
    const affectedFeatures = fbConfig.facebookFeatures
      .filter((featureId: PortalFeature) =>
        enabledFeatures.some((nav) => nav.id === featureId),
      )
      .map((featureId: PortalFeature) => {
        const feature = enabledFeatures.find((nav) => nav.id === featureId);
        return feature?.name || featureId;
      });

    if (affectedFeatures.length === 0) {
      return;
    }

    const featuresList = affectedFeatures.join(', ');

    this.confirmationService.confirm({
      header: '⚠️ Configuration Review Required',
      message: `Facebook account changes may affect the following features:<br><br><strong>${featuresList}</strong><br><br>Please review their configurations to avoid errors.`,
      icon: 'pi pi-info-circle',
      acceptLabel: 'Got it',
      rejectVisible: false,
      acceptButtonStyleClass: 'p-button-info',
      accept: () => {
        // User acknowledged the message
      }
    });
  }
}
