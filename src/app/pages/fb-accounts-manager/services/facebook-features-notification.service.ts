import { Injectable } from '@angular/core';
import { MessageService } from 'primeng/api';
import { ConfigService } from '../../../core';
import { PortalFeature } from '../../../core/types';
import { FbAccountsManagerConfig } from '../config/fb-accounts-manager.config';

@Injectable({
  providedIn: 'root',
})
export class FacebookFeaturesNotificationService {
  constructor(
    private messageService: MessageService,
    private configService: ConfigService,
  ) {}

  /**
   * Show info dialog about affected Facebook features after account changes
   */
  showAffectedFeaturesInfo(): void {
    const fbConfig = this.configService.getCustomerFeatureConfig<PortalFeature.FB_ACCOUNTS_MANAGER>(
      PortalFeature.FB_ACCOUNTS_MANAGER
    ) as FbAccountsManagerConfig;

    if (!fbConfig?.facebookFeatures || fbConfig.facebookFeatures.length === 0) {
      return;
    }

    // Get enabled features from navigation
    const enabledFeatures = this.configService.navigation;
    
    // Filter to only show enabled Facebook features
    const affectedFeatures = fbConfig.facebookFeatures
      .filter(featureId => enabledFeatures.some(nav => nav.id === featureId))
      .map(featureId => {
        const feature = enabledFeatures.find(nav => nav.id === featureId);
        return feature?.name || featureId;
      });

    if (affectedFeatures.length === 0) {
      return;
    }

    const featuresList = affectedFeatures.join(', ');
    
    this.messageService.add({
      severity: 'info',
      summary: 'Configuration Review Required',
      detail: `Facebook account changes may affect: ${featuresList}. Please review their configurations to avoid errors.`,
      life: 8000, // Show for 8 seconds
    });
  }
}
