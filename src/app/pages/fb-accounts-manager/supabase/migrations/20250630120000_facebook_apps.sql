-- Enable UUID extension
CREATE
EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create fbam_facebook_apps table
CREATE TABLE fbam_facebook_apps
(
  id         UUID                     DEFAULT uuid_generate_v4() PRIMARY KEY,
  name       TEXT NOT NULL,
  app_id     TEXT NOT NULL UNIQUE,
  app_secret TEXT NOT NULL,
  is_active  BOOLEAN                  DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create fbam_facebook_users table (renamed from facebook_tokens)
CREATE TABLE fbam_facebook_users
(
  id                  UUID                     DEFAULT uuid_generate_v4() PRIMARY KEY,
  app_id              TEXT NOT NULL REFERENCES fbam_facebook_apps (app_id) ON DELETE CASCADE,
  access_token        TEXT NOT NULL,
  facebook_user_id    TEXT NOT NULL, -- Facebook user ID
  name                TEXT NOT NULL, -- User's full name
  email               TEXT,          -- User's email (if available)
  profile_picture_url TEXT,          -- Profile picture URL
  is_long_lived       BOOLEAN                  DEFAULT TRUE,
  permissions         TEXT[],        -- Array of granted permissions
  expires_at          TIMESTAMP WITH TIME ZONE,
  last_login          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Ensure one user per app
  UNIQUE (app_id, facebook_user_id)
);

-- Create indexes for better performance
CREATE INDEX idx_fbam_facebook_apps_app_id ON fbam_facebook_apps (app_id);
CREATE INDEX idx_fbam_facebook_apps_is_active ON fbam_facebook_apps (is_active);

CREATE INDEX idx_fbam_facebook_users_app_id ON fbam_facebook_users (app_id);
CREATE INDEX idx_fbam_facebook_users_facebook_user_id ON fbam_facebook_users (facebook_user_id);
CREATE INDEX idx_fbam_facebook_users_expires_at ON fbam_facebook_users (expires_at);
CREATE INDEX idx_fbam_facebook_users_last_login ON fbam_facebook_users (last_login);

-- Enable Row Level Security (RLS)
ALTER TABLE fbam_facebook_apps ENABLE ROW LEVEL SECURITY;
ALTER TABLE fbam_facebook_users ENABLE ROW LEVEL SECURITY;

-- Create policies for fbam_facebook_apps - allow all operations for authenticated users
CREATE
POLICY "Enable all operations for authenticated users" ON fbam_facebook_apps
    FOR ALL USING (auth.role() = 'authenticated');

-- Create policies for fbam_facebook_users - allow all operations for authenticated users
CREATE
POLICY "Enable all operations for authenticated users" ON fbam_facebook_users
    FOR ALL USING (auth.role() = 'authenticated');

-- Create function to automatically set updated_at
CREATE
OR REPLACE FUNCTION set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    -- Always update updated_at
    NEW.updated_at
= NOW();

RETURN NEW;
END;
$$
LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for fbam_facebook_apps
CREATE TRIGGER fbam_facebook_apps_set_updated_at
  BEFORE INSERT OR
UPDATE ON fbam_facebook_apps
  FOR EACH ROW
  EXECUTE FUNCTION set_updated_at();

-- Create triggers for fbam_facebook_users
CREATE TRIGGER fbam_facebook_users_set_updated_at
  BEFORE INSERT OR
UPDATE ON fbam_facebook_users
  FOR EACH ROW
  EXECUTE FUNCTION set_updated_at();

-- Add comments for documentation
COMMENT
ON TABLE fbam_facebook_apps IS 'Facebook applications configured by users (FB Accounts Manager)';
COMMENT
ON TABLE fbam_facebook_users IS 'Facebook user accounts connected to apps (FB Accounts Manager)';

COMMENT
ON COLUMN fbam_facebook_apps.app_id IS 'Facebook App ID from Facebook Developer Console';
COMMENT
ON COLUMN fbam_facebook_apps.app_secret IS 'Facebook App Secret from Facebook Developer Console';

COMMENT
ON COLUMN fbam_facebook_users.facebook_user_id IS 'Facebook User ID from Facebook Graph API';
COMMENT
ON COLUMN fbam_facebook_users.name IS 'User full name from Facebook profile';
COMMENT
ON COLUMN fbam_facebook_users.email IS 'User email address (if permission granted)';
COMMENT
ON COLUMN fbam_facebook_users.profile_picture_url IS 'URL to user profile picture';
COMMENT
ON COLUMN fbam_facebook_users.is_long_lived IS 'Whether this is a long-lived token (60 days)';
COMMENT
ON COLUMN fbam_facebook_users.permissions IS 'Array of Facebook permissions granted to this user';
COMMENT
ON COLUMN fbam_facebook_users.last_login IS 'Timestamp of last successful login';

        -- Create fbam_facebook_ad_accounts table to map users to their accessible ad accounts
CREATE TABLE fbam_facebook_ad_accounts
(
  id              UUID                     DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id         UUID NOT NULL REFERENCES fbam_facebook_users (id) ON DELETE CASCADE,
  ad_account_id   TEXT NOT NULL,                         -- Facebook Ad Account ID (e.g., act_123456789)
  ad_account_name TEXT NOT NULL,
  account_status  INTEGER                  DEFAULT 1,    -- Facebook account status
  currency        TEXT                     DEFAULT 'USD',
  timezone_name   TEXT,
  business_id     TEXT,
  business_name   TEXT,
  permissions     TEXT[],                                -- Specific permissions for this ad account
  is_accessible   BOOLEAN                  DEFAULT TRUE, -- Whether the user can currently access this account
  last_synced     TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Ensure unique mapping per user-account combination
  UNIQUE (user_id, ad_account_id)
);

-- Add unique constraint for ad_account_id to prevent multiple users accessing same account
CREATE UNIQUE INDEX idx_fbam_facebook_ad_accounts_unique_account
  ON fbam_facebook_ad_accounts (ad_account_id) WHERE is_accessible = true;

-- Create indexes for performance
CREATE INDEX idx_fbam_facebook_ad_accounts_user_id ON fbam_facebook_ad_accounts (user_id);
CREATE INDEX idx_fbam_facebook_ad_accounts_ad_account_id ON fbam_facebook_ad_accounts (ad_account_id);
CREATE INDEX idx_fbam_facebook_ad_accounts_accessible ON fbam_facebook_ad_accounts (is_accessible) WHERE is_accessible = true;

-- Add trigger for updated_at
CREATE
OR REPLACE FUNCTION update_fbam_facebook_ad_accounts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at
= NOW();
RETURN NEW;
END;
$$
language 'plpgsql';

CREATE TRIGGER update_fbam_facebook_ad_accounts_updated_at
  BEFORE UPDATE
  ON fbam_facebook_ad_accounts
  FOR EACH ROW
  EXECUTE FUNCTION update_fbam_facebook_ad_accounts_updated_at();

-- Add comments
COMMENT
ON TABLE fbam_facebook_ad_accounts IS 'Mapping between Facebook users and their accessible ad accounts';
COMMENT
ON COLUMN fbam_facebook_ad_accounts.ad_account_id IS 'Facebook Ad Account ID with act_ prefix';
COMMENT
ON COLUMN fbam_facebook_ad_accounts.account_status IS 'Facebook account status (1=Active, 2=Disabled, etc.)';
COMMENT
ON COLUMN fbam_facebook_ad_accounts.is_accessible IS 'Whether user currently has access to this ad account';

-- Enable Row Level Security
ALTER TABLE fbam_facebook_ad_accounts ENABLE ROW LEVEL SECURITY;

-- Create policy for authenticated users only
CREATE
POLICY "Allow authenticated users to access ad accounts" ON fbam_facebook_ad_accounts
    FOR ALL USING (auth.role() = 'authenticated');

CREATE OR REPLACE FUNCTION get_token_by_account(account_id TEXT)
RETURNS TABLE(
  access_token TEXT,
  user_id TEXT,
  user_name TEXT,
  expires_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
RETURN QUERY
SELECT
  fu.access_token,
  fu.facebook_user_id as user_id,
  fu.name as user_name,
  fu.expires_at
FROM fbam_facebook_ad_accounts faa
       INNER JOIN fbam_facebook_users fu ON faa.user_id = fu.id
WHERE faa.ad_account_id = account_id
  AND faa.is_accessible = true
  LIMIT 1;
END;
$$;
