<div class="ad-winners-container">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <h1 class="page-title">
          <i class="pi pi-trophy"></i>
          Ad Winners
        </h1>
        <p class="page-description">
          Discover your top-performing ads across all campaigns and ad sets
        </p>
      </div>
      <div class="header-right">
        <p-button
          label="Configure Accounts"
          icon="pi pi-cog"
          [outlined]="true"
          severity="secondary"
          (onClick)="openConfigDialog()">
        </p-button>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="filters-section">
    <p-card styleClass="filter-card">
      <div class="filters-grid">
        <!-- Account Filter -->
        <div class="filter-group">
          <label for="account-filter">Account</label>
          <p-select
            (onChange)="onAccountChange()"
            [(ngModel)]="filters.account_id"
            [options]="accounts"
            [showClear]="true"
            inputId="account-filter"
            optionLabel="label"
            optionValue="value"
            placeholder="Select Account"
            styleClass="w-full">
          </p-select>
        </div>

        <!-- Campaign Filter -->
        <div class="filter-group">
          <label for="campaign-filter">Campaign</label>
          <p-select
            (onChange)="onCampaignChange()"
            [(ngModel)]="filters.campaign_id"
            [disabled]="!filters.account_id"
            [options]="campaigns"
            [showClear]="true"
            inputId="campaign-filter"
            optionLabel="label"
            optionValue="value"
            placeholder="Select Campaign"
            styleClass="w-full">
          </p-select>
        </div>

        <!-- Ad Set Filter -->
        <div class="filter-group">
          <label for="adset-filter">Ad Set</label>
          <p-select
            (onChange)="onAdSetChange()"
            [(ngModel)]="filters.adset_id"
            [disabled]="!filters.campaign_id"
            [options]="adSets"
            [showClear]="true"
            inputId="adset-filter"
            optionLabel="label"
            optionValue="value"
            placeholder="Select Ad Set"
            styleClass="w-full">
          </p-select>
        </div>

        <!-- Period Filter -->
        <div class="filter-group">
          <label for="period-filter">Period</label>
          <p-select
            (onChange)="onPeriodChange()"
            [(ngModel)]="selectedPeriod"
            [options]="periodOptions"
            [showClear]="true"
            inputId="period-filter"
            optionLabel="label"
            optionValue="value"
            placeholder="Select Period"
            styleClass="w-full">
            <ng-template #selectedItem>
              <span *ngIf="selectedPeriod">
                <ng-container *ngFor="let option of periodOptions">
                  <div *ngIf="option.value === selectedPeriod" class="selected-period">
                    <div class="period-dates-main">{{ option.week_start | date:'MMM d' }} - {{ option.week_end | date:'MMM d, y' }}</div>
                    <div class="period-label-main">{{ getPeriodLabelOnly(option.label) }}</div>
                  </div>
                </ng-container>
              </span>
            </ng-template>
            <ng-template #item let-option>
              <div class="period-option">
                <div class="period-dates">{{ option.week_start | date:'MMM d' }} - {{ option.week_end | date:'MMM d, y' }}</div>
                <div class="period-label">{{ getPeriodLabelOnly(option.label) }}</div>
              </div>
            </ng-template>
          </p-select>
        </div>


        <!-- Advanced Filters -->
        <div class="filter-group">
          <label for="min-spend">Min Spend ($)</label>
          <p-inputNumber
            (onInput)="onFiltersChange()"
            [(ngModel)]="filters.min_spend"
            currency="USD"
            id="min-spend"
            locale="en-US"
            mode="currency"
            placeholder="0.00"
            styleClass="w-full">
          </p-inputNumber>
        </div>
      </div>
      <div class="filter-actions">
        <p-button
          (onClick)="clearFilters()"
          icon="pi pi-filter-slash"
          label="Clear Filters"
          severity="secondary">
        </p-button>
      </div>
    </p-card>
  </div>


  <!-- Metric Formula Display -->
  <chm-metric-formula
    *ngIf="!loading && currentScoreConfiguration"
    [configuration]="currentScoreConfiguration">
  </chm-metric-formula>

  <!-- Results Section -->
  <div class="results-section">
    <!-- Stats Cards -->
    <div *ngIf="!loading" class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="pi pi-chart-line"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ totalRecords }}</div>
          <div class="stat-label">Total Winners</div>
        </div>
      </div>

      <!-- Performance Score Configuration Card -->
      <div (click)="openScoreBuilder()" class="performance-config-card">
        <div class="config-icon">
          <i class="pi pi-chart-line"></i>
        </div>
        <div class="config-content">
          <div class="config-name">{{ getCurrentConfigName() }}</div>
          <div class="config-label">Performance Score</div>
          <div class="config-metrics">
            <span class="primary-metric">{{ getPrimaryMetricName() }}</span>
            <span class="metrics-count">+{{ getActiveMetricsCount() - 1 }} more</span>
          </div>
        </div>
        <div class="config-edit">
          <i class="pi pi-pencil"></i>
        </div>
      </div>

      <div *ngIf="adWinners.length > 0" class="stat-card">
        <div class="stat-icon">
          <i class="pi pi-dollar"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ getTotalSpend() }}</div>
          <div class="stat-label">Total Spend</div>
        </div>
      </div>
    </div>


    <!-- Data Table -->
    <p-card styleClass="table-card">
      <ng-template pTemplate="header">
        <div class="table-header">
          <h3>
            <i class="pi pi-trophy"></i>
            Ad Winners</h3>
          <div class="table-actions">
            <div class="view-toggle">
              <p-button
                (onClick)="toggleViewMode()"
                [icon]="viewMode === 'table' ? 'pi pi-th-large' : 'pi pi-list'"
                [label]="viewMode === 'table' ? 'Cards' : 'Table'"
                severity="secondary"
              >
              </p-button>
            </div>
            <span class="p-input-icon-left">
              <i class="pi pi-search"></i>
              <input
                #globalFilter
                (input)="onGlobalFilter(searchTerm, dt)"
                [(ngModel)]="searchTerm"
                class="global-filter"
                pInputText
                placeholder="Search ads..."
                type="text">
            </span>
          </div>
        </div>
      </ng-template>

      <p-table
        #dt
        *ngIf="viewMode === 'table'"
        [globalFilterFields]="['name', 'adset.name', 'campaign.name', 'account.name']"
        [loading]="loading"
        [paginator]="true"
        [rowsPerPageOptions]="[10, 20, 50]"
        [rows]="20"
        [showCurrentPageReport]="true"
        [showGridlines]="true"
        [tableStyle]="{'width': '100%'}"
        [value]="adWinners"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
        stripedRows="true">

        <!-- Creative Column -->
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 80px;">Creative</th>
            <th pSortableColumn="name">
              Ad Name
              <p-sortIcon field="name"></p-sortIcon>
            </th>
            <th pSortableColumn="adset.name">
              Ad Set
              <p-sortIcon field="adset.name"></p-sortIcon>
            </th>
            <th pSortableColumn="campaign.name">
              Campaign
              <p-sortIcon field="campaign.name"></p-sortIcon>
            </th>
            <th pSortableColumn="performance_score">
              Score
              <p-sortIcon field="performance_score"></p-sortIcon>
            </th>
            <th pSortableColumn="weekly_insights.roas">
              ROAS
              <p-sortIcon field="weekly_insights.roas"></p-sortIcon>
            </th>
            <th pSortableColumn="weekly_insights.total_spend">
              Spend
              <p-sortIcon field="weekly_insights.total_spend"></p-sortIcon>
            </th>
            <th pSortableColumn="weekly_insights.conversion_rate">
              Conv. Rate
              <p-sortIcon field="weekly_insights.conversion_rate"></p-sortIcon>
            </th>
            <th pSortableColumn="weekly_insights.hook_rate">
              Hook Rate
              <p-sortIcon field="weekly_insights.hook_rate"></p-sortIcon>
            </th>
            <th pSortableColumn="weekly_insights.hold_rate">
              Hold Rate
              <p-sortIcon field="weekly_insights.hold_rate"></p-sortIcon>
            </th>
            <th pSortableColumn="weekly_insights.unique_outbound_clicks">
              Unique Clicks
              <p-sortIcon field="weekly_insights.unique_outbound_clicks"></p-sortIcon>
            </th>
            <th pSortableColumn="runningTimeDays">
              Running Time
              <p-sortIcon field="runningTimeDays"></p-sortIcon>
            </th>
            <th pSortableColumn="rank_in_adset">
              Rank
              <p-sortIcon field="rank_in_adset"></p-sortIcon>
            </th>
          </tr>
        </ng-template>

        <ng-template let-adWinner pTemplate="body">
          <tr>
            <!-- Creative -->
            <td>
              <div class="creative-cell">
                <div class="creative-wrapper">
                  <!-- Video thumbnail (only when video is available) -->
                  <div (click)="openImageModal(adWinner)" *ngIf="hasAvailableVideo(adWinner)"
                       class="video-thumbnail-wrapper">
                    <video
                      [poster]="getImageUrl(adWinner.creative)"
                      [src]="getVideoUrl(adWinner)"
                      class="ad-thumbnail video-thumbnail clickable"
                      muted
                      preload="metadata">
                    </video>
                    <div class="video-play-overlay">
                      <i class="pi pi-play"></i>
                    </div>
                  </div>
                  <!-- Regular image (when no available video OR when video exists but not available yet) -->
                  <img
                    (click)="openImageModal(adWinner)"
                    (error)="onImageError($event)"
                    *ngIf="!hasAvailableVideo(adWinner) && hasImage(adWinner.creative)"
                    [alt]="adWinner.name"
                    [src]="getImageUrl(adWinner.creative)"
                    class="ad-thumbnail clickable">
                  <!-- No media placeholder -->
                  <div
                    *ngIf="!hasAvailableVideo(adWinner) && !hasImage(adWinner.creative)"
                    class="no-image-placeholder">
                    <i class="pi pi-image"></i>
                  </div>
                  <div [attr.data-type]="getCreativeType(adWinner.creative)" class="creative-type-badge">
                    <i [ngClass]="{
                      'pi-video': hasVideo(adWinner) || getCreativeType(adWinner.creative) === 'video',
                      'pi-image': getCreativeType(adWinner.creative) === 'photo',
                      'pi-link': getCreativeType(adWinner.creative) === 'link',
                      'pi-th-large': getCreativeType(adWinner.creative) === 'template',
                      'pi-question': getCreativeType(adWinner.creative) === 'unknown'
                    }" class="pi"></i>
                  </div>
                </div>
              </div>
            </td>

            <!-- Ad Name -->
            <td>
              <div class="ad-name-cell">
                <span [title]="adWinner.name" class="ad-name">{{ adWinner.name }}</span>
                <p-tag
                  [severity]="adWinner.status === 'ACTIVE' ? 'success' : 'secondary'"
                  [value]="adWinner.status"
                  styleClass="status-tag">
                </p-tag>
              </div>
            </td>

            <!-- Ad Set -->
            <td>
              <span [title]="adWinner.adset?.name">{{ adWinner.adset?.name }}</span>
            </td>

            <!-- Campaign -->
            <td>
              <span [title]="adWinner.campaign?.name">{{ adWinner.campaign?.name }}</span>
            </td>

            <!-- Performance Score -->
            <td>
              <div class="score-cell">
                <p-progressBar
                  [styleClass]="'p-progressbar-' + getPerformanceColor(adWinner.performance_score)"
                  [style]="{'height': '8px', 'margin-bottom': '4px'}"
                  [value]="adWinner.performance_score">
                </p-progressBar>
                <span class="score-value">{{ adWinner.performance_score }}%</span>
              </div>
            </td>

            <!-- ROAS -->
            <td>
              <span class="metric-value">{{ formatNumber(adWinner.weekly_insights?.roas || 0) }}</span>
            </td>

            <!-- Spend -->
            <td>
              <span class="metric-value">{{ formatCurrency(adWinner.weekly_insights?.total_spend || 0) }}</span>
            </td>

            <!-- Conversion Rate -->
            <td>
              <span class="metric-value">{{ formatPercentage(adWinner.weekly_insights?.conversion_rate || 0) }}</span>
            </td>

            <!-- Hook Rate -->
            <td>
              <span class="metric-value">{{ formatPercentage(adWinner.weekly_insights?.hook_rate || 0) }}</span>
            </td>

            <!-- Hold Rate -->
            <td>
              <span class="metric-value">{{ formatPercentage(adWinner.weekly_insights?.hold_rate || 0) }}</span>
            </td>

            <!-- Unique Outbound Clicks -->
            <td>
              <span class="metric-value">{{ (adWinner.weekly_insights?.unique_outbound_clicks || 0) | number }}</span>
            </td>

            <!-- Running Time -->
            <td>
              <span class="metric-value running-time-value">
                {{ adWinner.runningTimeDays || getRunningTimeDays(adWinner.created_time) }}
              </span>
            </td>

            <!-- Rank -->
            <td>
              <p-tag
                styleClass="rank-tag">
                <span>{{ getRankIcon(adWinner.rank_in_adset) }} </span>
              </p-tag>
            </td>
          </tr>
        </ng-template>

        <!-- Loading Template -->
        <ng-template pTemplate="loadingbody">
          <tr>
            <td colspan="13">
              <div class="loading-row">
                <p-skeleton height="40px" styleClass="mb-2"></p-skeleton>
                <p-skeleton height="40px" styleClass="mb-2"></p-skeleton>
                <p-skeleton height="40px"></p-skeleton>
              </div>
            </td>
          </tr>
        </ng-template>

        <!-- Empty Template -->
        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="13">
              <div class="empty-state">
                <i class="pi pi-search empty-icon"></i>
                <h4>No ad winners found</h4>
                <p>Try adjusting your filters or check back later for new data.</p>
              </div>
            </td>
          </tr>
        </ng-template>
      </p-table>

      <!-- Cards View -->
      <div *ngIf="viewMode === 'cards'" class="cards-container">
        <div *ngIf="!loading" class="cards-grid">
          <div *ngFor="let adWinner of filteredAdWinners" class="ad-winner-card">
            <div class="card-header">
              <!-- Large Creative Section -->
              <div class="card-creative-section">
                <div class="creative-wrapper">
                  <!-- Video display (only when video is available) -->
                  <div (click)="openImageModal(adWinner)" *ngIf="hasAvailableVideo(adWinner)" class="video-wrapper">
                    <video
                      #videoElement
                      (loadedmetadata)="videoElement.currentTime = 1"
                      [src]="getVideoUrl(adWinner)"
                      class="card-creative-large video-display clickable"
                      muted
                      preload="metadata">
                    </video>
                    <div class="video-play-overlay-large">
                      <i class="pi pi-play"></i>
                    </div>
                  </div>
                  <!-- Image display (when no available video OR when video exists but not available yet) -->
                  <img
                    (click)="openImageModal(adWinner)"
                    (error)="onImageError($event)"
                    *ngIf="!hasAvailableVideo(adWinner) && hasImage(adWinner.creative)"
                    [alt]="adWinner.name"
                    [src]="getImageUrl(adWinner.creative)"
                    class="card-creative-large clickable">
                  <!-- No media placeholder -->
                  <div
                    *ngIf="!hasAvailableVideo(adWinner) && !hasImage(adWinner.creative)"
                    class="card-creative-placeholder-large">
                    <i class="pi pi-image"></i>
                  </div>
                  <div [attr.data-type]="getCreativeType(adWinner.creative)"
                       class="creative-type-badge card-type-badge-large">
                    <i [ngClass]="{
                      'pi-video': hasVideo(adWinner) || getCreativeType(adWinner.creative) === 'video',
                      'pi-image': getCreativeType(adWinner.creative) === 'photo',
                      'pi-link': getCreativeType(adWinner.creative) === 'link',
                      'pi-th-large': getCreativeType(adWinner.creative) === 'template',
                      'pi-question': getCreativeType(adWinner.creative) === 'unknown'
                    }" class="pi"></i>
                    <!-- Video pending indicator -->
                    <span *ngIf="hasVideoButNotAvailable(adWinner)" class="video-pending-indicator"
                          title="Video updating...">
                      <i class="pi pi-clock"></i>
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="card-info-section">
              <div class="card-info">
                <h4 [title]="adWinner.name" class="ad-name">{{ adWinner.name }}</h4>
                <p class="ad-hierarchy">
                  <span class="account-name">{{ adWinner.account?.name }}</span>
                  <i class="pi pi-angle-right separator"></i>
                  <span class="campaign-name">{{ adWinner.campaign?.name }}</span>
                  <i class="pi pi-angle-right separator"></i>
                  <span class="adset-name">{{ adWinner.adset?.name }}</span>
                </p>
              </div>
              <div class="card-rank">
                <p-tag
                  [severity]="getRankColor(adWinner.rank_in_adset)"
                  styleClass="rank-badge">
                  <span>{{ getRankIcon(adWinner.rank_in_adset) }} </span>
                </p-tag>
              </div>
            </div>

            <div class="card-body">
              <div class="performance-section">
                <div class="score-display">
                  <div class="score-label">Performance Score</div>
                  <div class="score-value-large">{{ adWinner.performance_score }}%</div>
                  <p-progressBar
                    [styleClass]="'p-progressbar-' + getPerformanceColor(adWinner.performance_score)"
                    [style]="{'height': '6px'}"
                    [value]="adWinner.performance_score">
                  </p-progressBar>
                </div>
              </div>

              <div class="metrics-grid">
                <div class="metric-item">
                  <div class="metric-label">ROAS</div>
                  <div class="metric-value">{{ formatNumber(adWinner.weekly_insights.roas || 0) }}</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">Spend</div>
                  <div class="metric-value">{{ formatCurrency(adWinner.weekly_insights.total_spend || 0) }}</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">Conv. Rate</div>
                  <div class="metric-value">{{ formatPercentage(adWinner.weekly_insights.conversion_rate || 0) }}</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">Hook Rate</div>
                  <div class="metric-value">{{ formatPercentage(adWinner.weekly_insights.hook_rate || 0) }}</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">Hold Rate</div>
                  <div class="metric-value">{{ formatPercentage(adWinner.weekly_insights.hold_rate || 0) }}</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">Traffic Quality</div>
                  <div class="metric-value">{{ formatPercentage(adWinner.weekly_insights.traffic_quality || 0) }}</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">Unique Clicks</div>
                  <div class="metric-value">{{ (adWinner.weekly_insights.unique_outbound_clicks || 0) | number }}</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">Impressions</div>
                  <div class="metric-value">{{ (adWinner.weekly_insights.total_impressions || 0) | number }}</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">Running Time</div>
                  <div class="metric-value running-time-value">
                    {{ adWinner.runningTimeDays || getRunningTimeDays(adWinner.created_time) }}
                  </div>
                </div>
              </div>
            </div>

            <div class="card-footer">
              <p-tag
                [severity]="adWinner.status === 'ACTIVE' ? 'success' : 'secondary'"
                [value]="adWinner.status"
                styleClass="status-badge">
              </p-tag>
              <span class="best-metric">
                Best at: {{ getBestMetricLabel(adWinner.best_metric) }}
              </span>
            </div>
          </div>
        </div>

        <!-- Loading Cards -->
        <div *ngIf="loading" class="cards-grid">
          <div *ngFor="let i of [1,2,3,4,5,6]" class="ad-winner-card loading-card">
            <p-skeleton height="200px" styleClass="mb-2"></p-skeleton>
            <p-skeleton height="100px"></p-skeleton>
          </div>
        </div>

        <!-- Empty State for Cards -->
        <div *ngIf="!loading && filteredAdWinners.length === 0" class="empty-state">
          <i class="pi pi-search empty-icon"></i>
          <h4>{{ adWinners.length === 0 ? 'No ad winners found' : 'No ads match your search' }}</h4>
          <p>{{ adWinners.length === 0 ? 'Try adjusting your filters or check back later for new data.' : 'Try a different search term or clear the search.' }}</p>
        </div>
      </div>
    </p-card>
  </div>

  <!-- Media Modal -->
  <chm-media-modal
    [(visible)]="showImageModal"
    [videoUrl]="selectedImage?.videoSource || null"
    [imageUrl]="selectedImage?.url || null"
    [title]="selectedImage?.adName || 'Media Preview'"
    [adId]="selectedImage?.adId || null"
    (videoUrlRefreshed)="onVideoUrlRefreshed($event)">
  </chm-media-modal>

  <!-- Performance Score Builder -->
  <chm-performance-score-builder
    (configurationApplied)="onScoreConfigurationApplied($event)"
    (configurationSaved)="onScoreConfigurationSaved($event)"
    [(visible)]="showScoreBuilder"
    [currentConfiguration]="currentScoreConfiguration"
    [realAdData]="adWinners">
  </chm-performance-score-builder>
</div>
