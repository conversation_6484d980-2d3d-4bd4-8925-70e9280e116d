import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ConfigService } from '../../../core';
import { VideoRefreshRequest, VideoRefreshResponse, UrlStatusCheckResponse } from '../models';

@Injectable({
  providedIn: 'root',
})
export class AdWinnersWebhookService {
  constructor(
    private http: HttpClient,
    private configService: ConfigService,
  ) {}

  /**
   * Refresh expired video URL by calling n8n webhook
   */
  refreshVideoUrl(adId: string): Observable<VideoRefreshResponse> {
    const url = `${this.configService.n8nBaseUrl}/webhook/ad-winners/ads/video/refresh`;
    const payload: VideoRefreshRequest = { ad_id: adId };
    return this.http.post<VideoRefreshResponse>(url, payload);
  }
}
