import { Injectable } from '@angular/core';
import { catchError, combineLatest, from, map, Observable, of } from 'rxjs';
import { SupabaseService } from '../../../core';
import {
  AdWinner,
  AdWinnerFilters,
  AdWinnerMetric,
  AdWinnerResponse,
  FilterOption,
  PerformanceScoreConfiguration,
  PeriodOption,
  WeeklyPeriod,
} from '../models';
import { PerformanceScoreConfigService } from './performance-score-config.service';

@Injectable({
  providedIn: 'root',
})
export class AdWinnerService {
  constructor(
    private supabaseService: SupabaseService,
  ) {}

  /**
   * Get all available weekly periods
   */
  getWeeklyPeriods(): Observable<WeeklyPeriod[]> {
    return from(
      this.supabaseService.client
        .from('adw_view_ad_insights_weekly_periods')
        .select('*')
        .order('week_start', { ascending: false }),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching weekly periods:', response.error);
          return [];
        }
        return response.data || [];
      }),
      catchError((error) => {
        console.error('Error fetching weekly periods:', error);
        return of([]);
      }),
    );
  }

  /**
   * Get dynamic period options based on available weekly periods (max 28 days)
   */
  getPeriodOptions(): Observable<PeriodOption[]> {
    return this.getWeeklyPeriods().pipe(
      map((periods) => {
        if (periods.length === 0) return [];

        const latestPeriod = periods[0]; // Most recent period
        const options: PeriodOption[] = [];

        // Generate dynamic periods based on available data
        const periodConfigs = [
          { days: 7, label: 'Last 7D' },
          { days: 14, label: 'Last 14D' },
          { days: 21, label: 'Last 21D' },
          { days: 28, label: 'Last 28D' },
        ];

        periodConfigs.forEach((config) => {
          const requiredWeeks = Math.ceil(config.days / 7);
          if (periods.length >= requiredWeeks) {
            const startPeriod = periods[requiredWeeks - 1]; // Get the period that's requiredWeeks back
            const startDate = new Date(startPeriod.week_start);
            const endDate = new Date(latestPeriod.week_end);

            // Check if this creates a unique period
            const isUnique = !options.some(
              (opt) =>
                opt.week_start === startPeriod.week_start &&
                opt.week_end === latestPeriod.week_end,
            );

            if (isUnique) {
              options.push({
                label: `${this.formatDateRange(startDate, endDate)}\n${config.label}`,
                value: `${config.days}d`,
                days: config.days,
                week_start: startPeriod.week_start,
                week_end: latestPeriod.week_end,
              });
            }
          }
        });

        // Add individual weekly periods (skip "This Week", start from "Last Week")
        periods
          .slice(1, Math.min(4, periods.length))
          .forEach((period, index) => {
            const startDate = new Date(period.week_start);
            const endDate = new Date(period.week_end);
            const weekLabel =
              index === 0 ? 'Last Week' : `${index + 2} Weeks Ago`;

            // Check if this creates a unique period
            const isUnique = !options.some(
              (opt) =>
                opt.week_start === period.week_start &&
                opt.week_end === period.week_end,
            );

            if (isUnique) {
              options.push({
                label: `${this.formatDateRange(startDate, endDate)}\n${weekLabel}`,
                value: `week_${period.week_start}`,
                days: 7,
                week_start: period.week_start,
                week_end: period.week_end,
              });
            }
          });

        return options;
      }),
    );
  }

  /**
   * Get all accounts for filter dropdown
   */
  getAccounts(): Observable<FilterOption[]> {
    return from(
      this.supabaseService.client
        .from('adw_accounts')
        .select('id, name')
        .order('name'),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching accounts:', response.error);
          return [];
        }
        return (response.data || []).map((account) => ({
          label: account.name,
          value: account.id,
        }));
      }),
      catchError((error) => {
        console.error('Error fetching accounts:', error);
        return of([]);
      }),
    );
  }

  /**
   * Get campaigns for specific account
   */
  getCampaigns(accountId?: string): Observable<FilterOption[]> {
    let query = this.supabaseService.client
      .from('adw_campaigns')
      .select('id, name');

    if (accountId) {
      query = query.eq('account_id', accountId);
    }

    return from(query.order('name')).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching campaigns:', response.error);
          return [];
        }
        return (response.data || []).map((campaign) => ({
          label: campaign.name,
          value: campaign.id,
        }));
      }),
      catchError((error) => {
        console.error('Error fetching campaigns:', error);
        return of([]);
      }),
    );
  }

  /**
   * Get ad sets for specific campaign
   */
  getAdSets(campaignId?: string): Observable<FilterOption[]> {
    let query = this.supabaseService.client.from('adw_adsets').select('id, name');

    if (campaignId) {
      query = query.eq('campaign_id', campaignId);
    }

    return from(query.order('name')).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching adsets:', response.error);
          return [];
        }
        return (response.data || []).map((adset) => ({
          label: adset.name,
          value: adset.id,
        }));
      }),
      catchError((error) => {
        console.error('Error fetching adsets:', error);
        return of([]);
      }),
    );
  }

  /**
   * Get ad winners based on filters
   */
  getAdWinners(filters: AdWinnerFilters = {}): Observable<AdWinnerResponse> {
    return combineLatest([
      this.getAdInsightsWithDetails(filters),
      this.getWeeklyPeriods(),
    ]).pipe(
      map(([insights, periods]) => {
        const adWinners = this.calculateAdWinners(insights);

        return {
          data: adWinners,
          total_count: adWinners.length,
          filters_applied: filters,
          periods_available: periods,
        };
      }),
      catchError((error) => {
        console.error('Error fetching ad winners:', error);
        return of({
          data: [],
          total_count: 0,
          filters_applied: filters,
          periods_available: [],
        });
      }),
    );
  }

  /**
   * Calculate performance score using custom configuration
   */
  calculatePerformanceScoreWithConfig(
    insight: any,
    config: PerformanceScoreConfiguration,
  ): number {
    let score = 0;
    let totalWeight = 0;

    config.metrics_config.metrics.forEach((metric) => {
      if (metric.weight > 0) {
        const value = this.getMetricValue(insight, metric.metric);
        if (value !== null) {
          let normalizedValue = 0;

          switch (metric.metric) {
            case 'roas':
              normalizedValue = Math.min(value * 10, 100);
              break;
            case 'conversion_rate':
            case 'hook_rate':
            case 'hold_rate':
            case 'traffic_quality':
            case 'outbound_ctr':
              normalizedValue = Math.min(value * 100, 100);
              break;
            case 'cpa':
            case 'cost_per_landing_page_view':
            case 'cost_per_add_to_cart':
              normalizedValue = Math.max(0, 100 - (value / 100) * 100);
              break;
            case 'avg_cpm':
              normalizedValue = Math.max(0, 100 - (value / 50) * 100);
              break;
            case 'total_spend':
            case 'total_impressions':
            case 'total_reach':
            case 'total_purchases':
            case 'total_purchase_value':
            case 'total_landing_page_views':
            case 'total_outbound_clicks':
            case 'total_video_plays_3s':
            case 'total_add_to_carts':
            case 'unique_outbound_clicks':
              // For volume metrics, normalize based on percentile ranking
              normalizedValue = Math.min((value / 10000) * 100, 100);
              break;
            case 'avg_frequency':
              normalizedValue = Math.min(value * 20, 100);
              break;
            case 'average_order_value':
              normalizedValue = Math.min((value / 200) * 100, 100);
              break;
          }

          score += normalizedValue * (metric.weight / 100);
          totalWeight += metric.weight;
        }
      }
    });

    return totalWeight > 0 ? Math.round(score) : 0;
  }

  private formatDateRange(startDate: Date, endDate: Date): string {
    const start = startDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
    const end = endDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
    return `${start} - ${end}`;
  }

  /**
   * Get ad insights with full details (ads, adsets, campaigns, accounts)
   */
  private getAdInsightsWithDetails(
    filters: AdWinnerFilters,
  ): Observable<any[]> {
    let query = this.supabaseService.client.from('adw_ad_insights_weekly').select(`
        *,
        ads:adw_ads!inner(
          id,
          name,
          created_time,
          status,
          video_id,
          creative,
          videos:adw_videos(
            id,
            source
          ),
          adsets:adw_adsets!inner(
            id,
            name,
            status,
            campaigns:adw_campaigns!inner(
              id,
              name,
              status,
              objective,
              accounts:adw_accounts!inner(
                id,
                name,
                status
              )
            )
          )
        )
      `);

    // Apply filters
    if (filters.account_id) {
      query = query.eq('account_id', filters.account_id);
    }

    if (filters.campaign_id) {
      query = query.eq('campaign_id', filters.campaign_id);
    }

    if (filters.adset_id) {
      query = query.eq('adset_id', filters.adset_id);
    }

    if (filters.week_start && filters.week_end) {
      // Range query for aggregated periods
      query = query.gte('week_start', filters.week_start);
      query = query.lte('week_end', filters.week_end);
    } else if (filters.week_start) {
      // Single week query (backward compatibility)
      query = query.eq('week_start', filters.week_start);
    }

    if (filters.min_spend) {
      query = query.gte('total_spend', filters.min_spend);
    }

    if (filters.min_impressions) {
      query = query.gte('total_impressions', filters.min_impressions);
    }

    return from(query.order('total_spend', { ascending: false })).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching ad insights:', response.error);
          return [];
        }
        return response.data || [];
      }),
    );
  }

  /**
   * Calculate ad winners based on performance metrics
   */
  private calculateAdWinners(insights: any[]): AdWinner[] {
    // First aggregate insights by ad_id to handle multiple weeks
    const adAggregates = this.aggregateInsightsByAd(insights);

    // Group aggregated insights by adset
    const adsetGroups = new Map<string, any[]>();

    adAggregates.forEach((aggregate) => {
      const adsetId = aggregate.adset_id;
      if (!adsetGroups.has(adsetId)) {
        adsetGroups.set(adsetId, []);
      }
      adsetGroups.get(adsetId)!.push(aggregate);
    });

    const winners: AdWinner[] = [];

    // Find winner in each adset
    adsetGroups.forEach((adsetInsights, adsetId) => {
      // Sort by performance score (using default ROAS-focused calculation)
      const sortedAds = adsetInsights.sort((a, b) => {
        const aScore = this.calculateDefaultPerformanceScore(a);
        const bScore = this.calculateDefaultPerformanceScore(b);
        return bScore - aScore; // Descending order
      });

      // Create winners with ranking
      sortedAds.forEach((aggregate, index) => {
        const performanceScore =
          this.calculateDefaultPerformanceScore(aggregate);
        const roasValue = this.getMetricValue(aggregate, 'roas');

        const winner: AdWinner = {
          id: aggregate.ads.id,
          adset_id: aggregate.adset_id,
          campaign_id: aggregate.campaign_id,
          account_id: aggregate.account_id,
          name: aggregate.ads.name,
          created_time: aggregate.ads.created_time,
          status: aggregate.ads.status,
          video_id: aggregate.ads.video_id,
          video: aggregate.ads.videos,
          creative: aggregate.ads.creative || {},
          created_at: aggregate.ads.created_at,
          updated_at: aggregate.ads.updated_at,
          adset: aggregate.ads.adsets,
          campaign: aggregate.ads.adsets.campaigns,
          account: aggregate.ads.adsets.campaigns.accounts,
          weekly_insights: aggregate, // This now contains aggregated data
          performance_score: performanceScore,
          rank_in_adset: index + 1,
          best_metric: 'roas', // Default to ROAS
          best_metric_value: roasValue || 0,
        };

        winners.push(winner);
      });
    });

    // Sort all winners by performance score
    return winners.sort((a, b) => b.performance_score - a.performance_score);
  }

  /**
   * Aggregate insights by ad_id for multi-week periods
   */
  private aggregateInsightsByAd(insights: any[]): any[] {
    const adGroups = new Map<string, any[]>();

    // Group insights by ad_id
    insights.forEach((insight) => {
      const adId = insight.ads.id;
      if (!adGroups.has(adId)) {
        adGroups.set(adId, []);
      }
      adGroups.get(adId)!.push(insight);
    });

    const aggregatedInsights: any[] = [];

    // Aggregate each ad's insights
    adGroups.forEach((adInsights, adId) => {
      if (adInsights.length === 0) return;

      // Use the first insight as the base structure
      const baseInsight = adInsights[0];

      // Aggregate numeric metrics
      const aggregated = {
        ...baseInsight,
        // Sum volume metrics
        total_impressions: adInsights.reduce(
          (sum, insight) => sum + (insight.total_impressions || 0),
          0,
        ),
        total_spend: adInsights.reduce(
          (sum, insight) => sum + (insight.total_spend || 0),
          0,
        ),
        total_reach: adInsights.reduce(
          (sum, insight) => sum + (insight.total_reach || 0),
          0,
        ),
        total_purchases: adInsights.reduce(
          (sum, insight) => sum + (insight.total_purchases || 0),
          0,
        ),
        total_purchase_value: adInsights.reduce(
          (sum, insight) => sum + (insight.total_purchase_value || 0),
          0,
        ),
        total_landing_page_views: adInsights.reduce(
          (sum, insight) => sum + (insight.total_landing_page_views || 0),
          0,
        ),
        total_outbound_clicks: adInsights.reduce(
          (sum, insight) => sum + (insight.total_outbound_clicks || 0),
          0,
        ),
        unique_outbound_clicks: adInsights.reduce(
          (sum, insight) => sum + (insight.unique_outbound_clicks || 0),
          0,
        ),
        total_video_plays_3s: adInsights.reduce(
          (sum, insight) => sum + (insight.total_video_plays_3s || 0),
          0,
        ),
        total_add_to_carts: adInsights.reduce(
          (sum, insight) => sum + (insight.total_add_to_carts || 0),
          0,
        ),

        // Calculate weighted averages for rate metrics
        avg_cpm: this.calculateWeightedAverage(
          adInsights,
          'avg_cpm',
          'total_impressions',
        ),
        avg_frequency: this.calculateWeightedAverage(
          adInsights,
          'avg_frequency',
          'total_reach',
        ),
        cpa: this.calculateWeightedAverage(
          adInsights,
          'cpa',
          'total_purchases',
        ),
        cost_per_landing_page_view: this.calculateWeightedAverage(
          adInsights,
          'cost_per_landing_page_view',
          'total_landing_page_views',
        ),
        cost_per_add_to_cart: this.calculateWeightedAverage(
          adInsights,
          'cost_per_add_to_cart',
          'total_add_to_carts',
        ),
        outbound_ctr: this.calculateWeightedAverage(
          adInsights,
          'outbound_ctr',
          'total_impressions',
        ),
        hook_rate: this.calculateWeightedAverage(
          adInsights,
          'hook_rate',
          'total_impressions',
        ),
        hold_rate: this.calculateWeightedAverage(
          adInsights,
          'hold_rate',
          'total_video_plays_3s',
        ),
        conversion_rate: this.calculateWeightedAverage(
          adInsights,
          'conversion_rate',
          'total_landing_page_views',
        ),
        traffic_quality: this.calculateWeightedAverage(
          adInsights,
          'traffic_quality',
          'total_outbound_clicks',
        ),

        // Calculate derived metrics
        week_start: adInsights[0].week_start, // Earliest week
        week_end: adInsights[adInsights.length - 1].week_end, // Latest week
      };

      // Calculate ROAS and AOV from aggregated data
      aggregated.roas =
        aggregated.total_spend > 0
          ? aggregated.total_purchase_value / aggregated.total_spend
          : 0;
      aggregated.average_order_value =
        aggregated.total_purchases > 0
          ? aggregated.total_purchase_value / aggregated.total_purchases
          : 0;

      aggregatedInsights.push(aggregated);
    });

    return aggregatedInsights;
  }

  /**
   * Calculate weighted average for rate metrics
   */
  private calculateWeightedAverage(
    insights: any[],
    metric: string,
    weightMetric: string,
  ): number {
    let totalWeightedValue = 0;
    let totalWeight = 0;

    insights.forEach((insight) => {
      const value = insight[metric];
      const weight = insight[weightMetric];

      if (value !== null && value !== undefined && weight > 0) {
        totalWeightedValue += value * weight;
        totalWeight += weight;
      }
    });

    return totalWeight > 0 ? totalWeightedValue / totalWeight : 0;
  }

  /**
   * Get metric value from insight data
   */
  private getMetricValue(insight: any, metric: AdWinnerMetric): number | null {
    const value = insight[metric];
    return value !== null && value !== undefined && !isNaN(value)
      ? Number(value)
      : null;
  }

  // Old calculatePerformanceScore method removed - now using configuration-based approach

  /**
   * Calculate default performance score (E-commerce focused)
   */
  private calculateDefaultPerformanceScore(insight: any): number {
    // Default E-commerce focused weights
    const defaultWeights = {
      roas: 35,
      conversion_rate: 30,
      traffic_quality: 15,
      cpa: 10,
      hook_rate: 5,
      hold_rate: 5,
    };

    let score = 0;
    let totalWeight = 0;

    Object.entries(defaultWeights).forEach(([metric, weight]) => {
      const value = this.getMetricValue(insight, metric as AdWinnerMetric);
      if (value !== null) {
        let normalizedValue = 0;

        switch (metric) {
          case 'roas':
            normalizedValue = Math.min(value * 10, 100);
            break;
          case 'conversion_rate':
          case 'hook_rate':
          case 'hold_rate':
          case 'traffic_quality':
            normalizedValue = Math.min(value * 100, 100);
            break;
          case 'cpa':
            normalizedValue = Math.max(0, 100 - (value / 100) * 100);
            break;
        }

        score += normalizedValue * (weight / 100);
        totalWeight += weight;
      }
    });

    return totalWeight > 0 ? Math.round(score) : 0;
  }
}
